part of 'face_video_capture_bloc.dart';

/// {@template face_video_capture_event}
/// Base class for all face video capture events.
/// {@endtemplate}
abstract class FaceVideoCaptureEvent extends Equatable {
  /// {@macro face_video_capture_event}
  const FaceVideoCaptureEvent();

  @override
  List<Object?> get props => [];
}

/// {@template initialize_camera_event}
/// Event to initialize the camera and face detection services.
/// {@endtemplate}
class InitializeCamera extends FaceVideoCaptureEvent {
  /// {@macro initialize_camera_event}
  const InitializeCamera();

  @override
  String toString() => 'InitializeCamera()';
}

/// {@template start_countdown_event}
/// Event to begin the countdown before recording starts.
/// {@endtemplate}
class StartCountdown extends FaceVideoCaptureEvent {
  /// {@macro start_countdown_event}
  const StartCountdown();

  @override
  String toString() => 'StartCountdown()';
}

/// {@template countdown_tick_event}
/// Event fired for each countdown tick.
/// {@endtemplate}
class CountdownTick extends FaceVideoCaptureEvent {
  /// {@macro countdown_tick_event}
  const CountdownTick(this.remainingSeconds);

  /// Number of seconds remaining in the countdown
  final int remainingSeconds;

  @override
  List<Object> get props => [remainingSeconds];

  @override
  String toString() => 'CountdownTick(remainingSeconds: $remainingSeconds)';
}

/// {@template start_recording_event}
/// Event to begin video recording.
/// {@endtemplate}
class StartRecording extends FaceVideoCaptureEvent {
  /// {@macro start_recording_event}
  const StartRecording();

  @override
  String toString() => 'StartRecording()';
}

/// {@template stop_recording_event}
/// Event to stop video recording and begin validation.
/// {@endtemplate}
class StopRecording extends FaceVideoCaptureEvent {
  /// {@macro stop_recording_event}
  const StopRecording();

  @override
  String toString() => 'StopRecording()';
}

/// {@template video_recording_completed_event}
/// Event fired when CamerAwesome completes video recording with actual file
/// path.
/// {@endtemplate}
class VideoRecordingCompleted extends FaceVideoCaptureEvent {
  /// {@macro video_recording_completed_event}
  const VideoRecordingCompleted({
    required this.videoPath,
  });

  /// The actual file path where CamerAwesome saved the video
  final String videoPath;

  @override
  List<Object?> get props => [videoPath];

  @override
  String toString() => 'VideoRecordingCompleted(videoPath: $videoPath)';
}

/// {@template process_frame_event}
/// Event to process a camera frame for face detection.
/// {@endtemplate}
class ProcessFrame extends FaceVideoCaptureEvent {
  /// {@macro process_frame_event}
  const ProcessFrame(this.detectionResult);

  /// Result of face detection for this frame
  final FaceDetectionResult detectionResult;

  @override
  List<Object> get props => [detectionResult];

  @override
  String toString() => 'ProcessFrame(detectionResult: $detectionResult)';
}

/// {@template recording_progress_event}
/// Event to update recording progress.
/// {@endtemplate}
class RecordingProgress extends FaceVideoCaptureEvent {
  /// {@macro recording_progress_event}
  const RecordingProgress(this.elapsedTime, this.remainingTime);

  /// Time elapsed since recording started
  final Duration elapsedTime;

  /// Time remaining in the recording
  final Duration remainingTime;

  @override
  List<Object> get props => [elapsedTime, remainingTime];

  @override
  String toString() => 'RecordingProgress(elapsed: ${elapsedTime.inSeconds}s, '
      'remaining: ${remainingTime.inSeconds}s)';
}

/// {@template reset_capture_event}
/// Event to reset the capture process to initial state.
/// {@endtemplate}
class ResetCapture extends FaceVideoCaptureEvent {
  /// {@macro reset_capture_event}
  const ResetCapture();

  @override
  String toString() => 'ResetCapture()';
}

/// {@template face_detection_status_changed_event}
/// Event fired when face detection status changes for recording readiness.
/// {@endtemplate}
class FaceDetectionStatusChanged extends FaceVideoCaptureEvent {
  /// {@macro face_detection_status_changed_event}
  const FaceDetectionStatusChanged({
    required this.canStartRecording,
    required this.detectionResult,
  });

  /// Whether recording can be started based on face detection
  final bool canStartRecording;

  /// Current face detection result
  final FaceDetectionResult detectionResult;

  @override
  List<Object> get props => [canStartRecording, detectionResult];

  @override
  String toString() => 'FaceDetectionStatusChanged('
      'canStartRecording: $canStartRecording, '
      'coverage: ${detectionResult.coveragePercentage.toStringAsFixed(1)}%)';
}

/// {@template abort_countdown_event}
/// Event to abort the countdown when face detection requirements are not met.
/// {@endtemplate}
class AbortCountdown extends FaceVideoCaptureEvent {
  /// {@macro abort_countdown_event}
  const AbortCountdown({required this.reason});

  /// Reason for aborting the countdown
  final String reason;

  @override
  List<Object> get props => [reason];

  @override
  String toString() => 'AbortCountdown(reason: $reason)';
}

/// {@template dispose_resources_event}
/// Event to dispose of camera and detection resources.
/// {@endtemplate}
class DisposeResources extends FaceVideoCaptureEvent {
  /// {@macro dispose_resources_event}
  const DisposeResources();

  @override
  String toString() => 'DisposeResources()';
}
